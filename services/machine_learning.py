import requests
import os
from typing import Dict, Any

# Get the base URL from environment variables
SERVICES_BASE_URL = os.environ.get("SERVICES_BASE_URL")

def create_plans_and_routines(access_token: str, user_message: str) -> Dict[str, Any]:
    """
    Sends a request to create or update plans and routines in the machine learning service.
    
    Args:
        access_token (str): User's access token for authentication.
        data (Dict[str, Any]): A dictionary containing key-value pairs to be sent to the machine learning service.
        Example:
        {
            "goal": "Loosing weight"
        }

    Returns:
        Dict[str, Any]: The API response as a dictionary.
    """
    url = f'{SERVICES_BASE_URL}/machinelearning/api/v1/message'

    headers = {
        "X-Access-Token": access_token,
        "Content-Type": "application/json"
    }

    payload = { 
        "message": user_message 
    }

    try:
        response = requests.post(url, headers=headers, json=payload)
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"Error calling ML service: {e}")
        return {"error": str(e)}
    