import os
from datetime import datetime
from handlers.helpers import extract_dates_llm
from handlers.tools import fetch_user_profile_tool, fetch_activity_tool, fetch_sleep_tool, fetch_target_tool, fetch_meal_logs_tool, fetch_assigned_meal_plans_tool, fetch_exercise_tool, fetch_exercise_templates_tool, fetch_assigned_exercise_routines_tool, fetch_mindfulness_tool, fetch_mindfulness_templates_tool, fetch_assigned_mindfulness_routines_tool, fetch_water_logs_tool, fetch_bp_tool, fetch_bg_tool, fetch_activity_tool, fetch_egvs_tool, fetch_spo2_tool, fetch_heart_rate_tool, fetch_resting_heart_rate_tool, fetch_hrv_tool, fetch_vo2_tool, fetch_ecg_tool, fetch_height_tool, fetch_weight_tool, fetch_fat_tool, fetch_bmi_tool, fetch_temp_tool, fetch_waist_size_tool, fetch_hip_size_tool, fetch_chest_size_tool, fetch_arm_size_tool, fetch_quad_size_tool, fetch_user_recommendations_tool, fetch_devices_tool, fetch_static_jsons_tool, set_target_tool, update_user_profile_tool, create_plans_and_routines_tool

from handlers.tools import fetch_last_messages, upsert_session_message, retrieve_relevant_messages

from langchain_openai import ChatOpenAI
from langchain.agents import initialize_agent, AgentType
from langchain.memory import ConversationBufferMemory

# Initialize OpenAI
OPENAI_API_KEY = os.environ.get("OPENAI_API_KEY")
if not OPENAI_API_KEY:
    print("Required Env variables not found")
    exit(1)

# Load OpenAI Model
llm = ChatOpenAI(model_name="gpt-4.1-mini", temperature=0.5, openai_api_key=OPENAI_API_KEY)

# Define Agent Memory
memory = ConversationBufferMemory(memory_key="chat_history", return_messages=True)

# Initialize LangChain agent with tools
all_tools = [
    fetch_user_profile_tool, 
    fetch_activity_tool, 
    fetch_sleep_tool, 
    fetch_target_tool, 
    fetch_meal_logs_tool, 
    fetch_assigned_meal_plans_tool,
    fetch_exercise_tool, 
    fetch_exercise_templates_tool,
    fetch_assigned_exercise_routines_tool,
    fetch_mindfulness_tool, 
    fetch_mindfulness_templates_tool,
    fetch_assigned_mindfulness_routines_tool,
    fetch_water_logs_tool, 
    fetch_bp_tool, 
    fetch_bg_tool,
    fetch_activity_tool,
    fetch_egvs_tool,
    fetch_spo2_tool,
    fetch_heart_rate_tool,
    fetch_resting_heart_rate_tool,
    fetch_hrv_tool,
    fetch_vo2_tool,
    fetch_ecg_tool,
    fetch_height_tool,
    fetch_weight_tool,
    fetch_fat_tool,
    fetch_bmi_tool,
    fetch_temp_tool,
    fetch_waist_size_tool,
    fetch_hip_size_tool,
    fetch_chest_size_tool,
    fetch_arm_size_tool,
    fetch_quad_size_tool,
    fetch_user_recommendations_tool,
    fetch_devices_tool,
    fetch_static_jsons_tool,
    set_target_tool,
    update_user_profile_tool,
    create_plans_and_routines_tool
]
    

agent = initialize_agent(
    llm=llm,
    tools=all_tools,
    agent=AgentType.OPENAI_FUNCTIONS,
    memory=memory,
    verbose=True
)

def ask_question(user_id: str, query: str, access_token: str = None):
    """Handles user queries by injecting relevant session memory and storing updated responses."""

    # Step 1: Extract date range using LLM
    TODAY_DATE = datetime.today().strftime("%Y-%m-%d")
    # date_info = extract_dates_llm(query)
    # start_date = date_info.start_date
    # end_date = date_info.end_date
    # print("Dates captured: ", start_date, end_date)

    # Step 2: Fetch relevant past sessions by vector similarity
    relevant_sessions = retrieve_relevant_messages(user_id, query, k=3)

    # Step 3: Fetch latest session (by timestamp)
    recent_messages = fetch_last_messages(user_id, 3)

    # Step 4: Compile context from relevant sessions + recent conversation
    chat_history = ConversationBufferMemory(memory_key="chat_history", return_messages=True)

    for session in relevant_sessions.get("hits", {}).get("hits", []):
        for msg in session["_source"]["messages"]:
            if msg["role"] == "user":
                chat_history.chat_memory.add_user_message(msg["content"])
            elif msg["role"] == "assistant":
                chat_history.chat_memory.add_ai_message(msg["content"])

    for msg in recent_messages:
        if msg["role"] == "user":
            chat_history.chat_memory.add_user_message(msg["content"])
        elif msg["role"] == "assistant":
            chat_history.chat_memory.add_ai_message(msg["content"])

    # Step 5: Construct full query for agent
    full_query = f"User ID: {user_id}, Extract the start and end date from the user's query considering today's date is {TODAY_DATE}. User Query: {query}. Access Token: {access_token}"
    print("Full Query: ", full_query)

    # Step 7: Initialize agent with memory
    chat_agent = initialize_agent(
        llm=llm,
        tools=all_tools,
        agent=AgentType.OPENAI_FUNCTIONS,
        memory=chat_history,
        verbose=True
    )

    # Step 6: Run agent
    response = chat_agent.invoke(full_query)
    final_output = response["output"] if isinstance(response, dict) and "output" in response else str(response)

    # Step 7: Store user + assistant messages into session DB
    upsert_session_message(
        user_id=user_id,
        new_messages=[
            {"role": "user", "content": query},
            {"role": "assistant", "content": final_output}
        ]
    )

    return final_output

def nudge_and_insights(user_id: str, query: str):
    """Handles nudges, insights and anomalies by extracting date ranges using LLM and fetching relevant data."""
    
    # Step 1: Extract date range using LLM
    date_info = extract_dates_llm(query)
    start_date = date_info.start_date
    end_date = date_info.end_date
    print("Dates captured: ", start_date, end_date)

    # Step 2: Modify query to include structured date range
    full_query = f"User ID: {user_id}, Date range: {start_date} to {end_date}. {query}"
    print("Full Query: ", full_query)

    # Step 3: Pass to LangChain agent for execution
    response = agent.invoke(full_query)

    # Step 4: Handle response
    if isinstance(response, dict) and "output" in response:
        return response["output"]
    elif isinstance(response, str):
        return response
    else:
        return str(response)


if __name__=='__main__':
    ask_question(user_id="test-user", query="How many steps did I walk in the past week?", access_token='123')
