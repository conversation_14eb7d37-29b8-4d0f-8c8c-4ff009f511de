from uuid import uuid4
from typing import Optional
from datetime import datetime, timedelta
from handlers.helpers import extract_response_message
from handlers.tools import store_user_recommendations
from fastapi import FastAPI, APIRouter, Request, HTTPException, Query

from models import *
from handlers import agent, auth, tools

# Initialize FastAPI app
app = FastAPI()

# Router with the prefix
router = APIRouter(prefix="/agent")

# Health check endpoint
@router.get("/healthcheck")
def healthcheck():
    return {"status": "healthy"}

# Chat endpoint
@router.post("/api/v1/chat")
def chat(input_body: ChatRequest, request: Request, user_guid: Optional[str] = None):
    x_access_token = request.headers.get('x-access-token')
    try:
        jwt_payload = auth.decodeJWT(x_access_token)
    except:
        raise HTTPException(status_code=403, detail="Invalid token or expired token.")
    if jwt_payload:
        request.state.userId = jwt_payload["user_guid"]
        request.state.token = x_access_token
    else:
        raise HTTPException(status_code=403, detail="Invalid token or expired token.")
    user_id = user_guid if user_guid else request.state.userId
    print("Request from user Id:", user_id)
    print(f"Received message: {input_body.message}")
    try:
        response = agent.ask_question(user_id, query=input_body.message, access_token=request.state.token)
        return {"response": response}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# Chat History endpoint
@router.get("/api/v1/chat-history")
def get_chat_history(
    request: Request, user_guid: Optional[str] = None, 
    query: Optional[str] = None, page: int = 1, limit: int = 5
):
    try:
        x_access_token = request.headers.get('x-access-token')
        try:
            jwt_payload = auth.decodeJWT(x_access_token)
        except:
            raise HTTPException(status_code=403, detail="Invalid token or expired token.")
        if jwt_payload:
            request.state.userId = jwt_payload["user_guid"]
            request.state.token = x_access_token
        else:
            raise HTTPException(status_code=403, detail="Invalid token or expired token.")
        user_id = user_guid if user_guid else request.state.userId
        print("Request from user Id:", user_id)
        print(f"Received message: {query}")

        results = tools.retrieve_chat_history(user_id, query, page, limit)
        return { "results": results, "page": page, "limit": limit }
    except Exception as e:
        print("Error occured while retreiving chat history", e)
        return { "success": False, "error": "Error occured while retreiving chat history" }

# Chat Feedback
@router.patch("/api/v1/chat/{message_id}")
async def update_feedback(message_id: str, feedback_update: FeedbackUpdate):
    success = tools.update_message_feedback(message_id, feedback_update.feedback)
    if not success:
        raise HTTPException(status_code=404, detail="Message not found")
    
    return {"message": "Feedback updated successfully"}


msg_type = {
    "nudge": """You are a helpful and motivating wellness coach helping users stay consistent with their weekly health targets. Use the "profile" and "target_achievements" tools to understand the user's 90 day goals and weekly habits over the past 4 weeks. 
Your task is to generate a single nudge Highlighting progress — mention any improvements or positive trends while offering encouragement — celebrate consistency and effort. And suggest an action — either recommend continuing the habit, slightly adjusting targets, or celebrating completion.
The response should be in JSON format:
{{
  "title": "Some encouraging title",
  "message": "Acknowledges the user's effort, highlights specific areas where targets were not met last week and gently nudges them to focus on these areas.",
  "priority": "low,medium,high"
}}
Keep the tone supportive, brief, and goal-focused. Use tools to query user logs. Ensure your message is personalized and data-informed.
""",
    "insight": """You are a wellness insights coach. Your task is to analyze the user's recent health and habit tracking data to identify positive behavioral trends or meaningful changes for the category: {category}.
Goal: Create a short, personalized, data-driven insight that:
- Highlights any positive trend or improvement from the past week or more.
- Reinforces this behavior to encourage continued progress.
- Suggests a next-step action to build upon this improvement.
Avoid generic praise—base everything on actual user data. Format: {{ "title": "...", "message": "...", "priority": "low,medium,high"}}""",
    "anomaly": """You are a wellness monitoring assistant. Review the user's recent health and habit tracking data for category: {category} to identify behavioral anomalies or deviations from usual patterns.
Look for issues like missing logs, data gaps, sugar spikes/dips, or sudden drops in activity/sleep.
Goal: Return a JSON response that:
- Clearly describes the anomaly
- Encourages reflection and suggests one concrete action for improvement
Ensure the response is accurate and data-informed. Format: {{ "title": "...", "message": "...", "priority": "low,medium,high"}}
"""
}

# Nudge endpoint
@router.post("/api/v1/nudge")
def nudge_chat(input_body: NudgeRequest, request: Request, user_guid: Optional[str] = None):
    x_access_token = request.headers.get('x-access-token')
    try:
        jwt_payload = auth.decodeJWT(x_access_token)
    except:
        raise HTTPException(status_code=403, detail="Invalid token or expired token.")
    if jwt_payload:
        request.state.userId = jwt_payload["user_guid"]
        request.state.token = x_access_token
    else:
        raise HTTPException(status_code=403, detail="Invalid token or expired token.")
    user_id = user_guid if user_guid else request.state.userId
    print("Request from user Id:", user_id)

    print(f"Received message: {input_body.type}, {input_body.category}")
    try:
        prompt = msg_type[input_body.type].format(category=input_body.category)

        response = agent.nudge_and_insights(user_id, query=prompt)
        response = extract_response_message(response)
        
        # Generate timestamp and TTL
        now = datetime.utcnow()
        ttl = now + timedelta(days=3)

        # Build OpenSearch document
        document = {
            "id": str(uuid4()),
            "userId": user_id,
            "type": input_body.type,
            "content": {
                "title": response["title"],
                "message": response["message"]
            },
            "timestamp": now.isoformat() + "Z",
            "category": input_body.category,
            "priority": response.get("priority", "low"),
            "ttl": ttl.isoformat() + "Z",
            "status": "open"
        }

        # Index the document
        store_user_recommendations(document)
        return {"response": document}

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

def nudge(user_id, type, category):
    print(f"Received message: {user_id}, {type}, {category}")
    try:
        prompt = msg_type[type]
        try: prompt = prompt.format(category=category)
        except: pass
        response = agent.nudge_and_insights(user_id, query=prompt)
        response = extract_response_message(response)
        
        # Generate timestamp and TTL
        now = datetime.utcnow()
        ttl = now + timedelta(days=3)

        # Build OpenSearch document
        document = {
            "id": str(uuid4()),
            "userId": user_id,
            "type": type,
            "content": {
                "title": response["title"],
                "message": response["message"]
            },
            "timestamp": now.isoformat() + "Z",
            "category": category,
            "priority": response.get("priority", "low"),
            "ttl": ttl.isoformat() + "Z",
            "status": "open"
        }

        # Index the document
        store_user_recommendations(document)
        print(document)

    except Exception as e:
        print("Error occured: ", e)

# Lambda handler
app.include_router(router)
