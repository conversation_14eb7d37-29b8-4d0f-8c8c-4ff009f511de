# AIAgent


**Overview**  
This project builds an AI-powered Agent for users to query their **health data** (steps, calories, flights climbed, etc.) stored in **OpenSearch**. It uses **Lang<PERSON>hain**, **OpenAI GPT-4**, and **FastAPI** to fetch and summarize data dynamically.  

---

## **🛠 Features**
✅ **Natural Language Queries** – Users can ask questions like:  
   - *"How many steps did I walk last week?"*  
   - *"Compare my step goal achievement from last month to this month."*  
   - *"Did I burn more calories today than yesterday?"*  

✅ **Dynamic Date Extraction** – The LLM understands time-based queries (e.g., *"last 3 days," "past month"*).  

✅ **Intelligent OpenSearch Queries** – The agent converts user questions into OpenSearch queries automatically.  

✅ **LLM Summarization** – The AI provides **human-friendly** summaries instead of raw data dumps.  

---

## **📂 Project Structure**
```
📦 AIAgent
├── lambda.py                 # Lambda Handler
├── app.py                    # FastAPI server
├── handlers/
    ├── chat_query.py         # Lang<PERSON>hain agent setup
    ├── tools.py              # OpenSearch query tools
    ├── date_parser.py        # LLM-powered date extraction
├── requirements.txt          # Dependencies
└── README.md                 # Project documentation
```

---

## **Quick Setup**

### **1️⃣ Install Dependencies**
```sh
python3 -m venv venv
. ./venv/bin/activate
pip install -r requirements.txt
```

### **2️⃣ Configure Env Vars**
```shell
export OPENAI_API_KEY = "sk-***"
export OPENSEARCH_HOST =  "search-***"
```

### **3️⃣ Start the API Server**
```sh
uvicorn app:app --reload
```

---

## API Endpoints

#### **HealthCheck**
```http
GET /healthcheck
```
#### **Response**
```json
{
  "status": "healthy"
}
```

### **Ask a Question**
#### **Request**
```http
POST /chat?user_id=123&query=How many steps did I walk last week?
```
#### **Response**
```json
{
  "response": "You walked a total of 42,500 steps last week, averaging 6,071 steps per day."
}
```

--

## How Temperature Works

The **temperature** parameter in an LLM (like OpenAI's GPT models) controls the **randomness and creativity** of the model’s responses.  


- **Low Temperature (`0.0 - 0.3`)** → More **deterministic** and **factual** responses.  
  - The model picks the **most probable** words.  
  - Best for structured tasks like **date extraction, code generation, or database queries**.  
  - Example: *Precise responses for structured data retrieval.*  

- **Medium Temperature (`0.4 - 0.7`)** → **Balanced** creativity & accuracy.  
  - Useful for **general conversation** and **customer support chatbots**.  

- **High Temperature (`0.8 - 1.5`)** → More **creative & diverse** responses.  
  - The model **randomly explores** different word choices.  
  - Best for **storytelling, brainstorming, and open-ended discussions**.  

## Dual Invocation

For invoking the lambda function from SQS send the following in SQS body
```json
{
  "Records": [
    {
      "eventSource": "aws:sqs",
      "body": { "user_id": "uuid", "type": "nudge/insight/anomaly", "category": "meallogs" }
    }
  ]
}
```
